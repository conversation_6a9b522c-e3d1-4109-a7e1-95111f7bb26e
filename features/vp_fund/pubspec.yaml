name: vp_fund
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common
  vp_auth:
    path: ../../features/vp_auth

  shimmer:
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  syncfusion_flutter_charts: ^29.2.7
  tiengviet: 1.0.0
  syncfusion_flutter_pdfviewer: 29.1.35
  collection: ^1.19.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  freezed:
 
dependency_overrides:
  intl: ^0.19.0

flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    outputs:
      class_name: VpFundAssets
      package_parameter_enabled: true

    # Optional
  integrations:
    image: true
    flutter_svg: true
    
 
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/

flutter_intl:
  enabled: true
  class_name: VPFundLocalize