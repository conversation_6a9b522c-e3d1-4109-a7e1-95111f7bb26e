import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_fund/common/common.dart';
import 'package:vp_fund/generated/assets.gen.dart';
import 'package:vp_fund/generated/l10n.dart';
import 'package:vp_fund/screen/efund/fund_detail/fund_detail_bloc.dart';
import 'package:vp_fund/screen/efund/widget/fund_growth_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../common/widget/cached_image.dart';
import '../../../../data/data.dart';

class FundDetailHeaderComponent extends StatelessWidget {
  const FundDetailHeaderComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: vpColor.backgroundElevation0,
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FundDetailBloc, FundDetailState>(
            buildWhen:
                (previous, current) =>
                    previous.fundOfferModel != current.fundOfferModel,
            builder: (context, state) {
              final logoLink = state.fundOfferModel?.logo ?? '';
              final symbol = state.fundOfferModel?.code ?? '';
              final companyName = state.fundOfferModel?.shortNameCompany ?? '';
              final type = FundTypeExtension.convertType(
                state.fundOfferModel?.type,
              );
              final shortName = state.fundOfferModel?.shortName ?? '';
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CachedImage(logoLink: logoLink),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              symbol,
                              style: context.textStyle.subtitle14?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: context.colors.textPrimary,
                              ),
                            ),
                            Text(
                              companyName,
                              style: context.textStyle.captionRegular?.copyWith(
                                color: context.colors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        type.title,
                        style: context.textStyle.subtitle14?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    shortName,
                    style: context.textStyle.subtitle16?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: vpColor.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              );
            },
          ),
          Row(
            children: [
              Expanded(
                child: BlocBuilder<FundDetailBloc, FundDetailState>(
                  buildWhen:
                      (previous, current) =>
                          previous.fundOfferModel != current.fundOfferModel,
                  builder: (context, state) {
                    final type = InvestLevelRiskExtension.convertType(
                      state.fundOfferModel?.investRiskLevel,
                    );
                    return _item(
                      title: VPFundLocalize.current.fund_level_of_risk,
                      value: type.title,
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: BlocBuilder<FundDetailBloc, FundDetailState>(
                  buildWhen:
                      (previous, current) =>
                          previous.fundDetailForTabModel !=
                          current.fundDetailForTabModel,
                  builder: (context, state) {
                    final nav = state.fundDetailForTabModel?.nav;
                    return _item(
                      title: VPFundLocalize.current.fund_nav,
                      value: '${nav != null ? nav.toFormat2() : ''} đ',
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: BlocBuilder<FundDetailBloc, FundDetailState>(
                  buildWhen:
                      (previous, current) =>
                          previous.fundDetailModel != current.fundDetailModel ||
                          previous.timeSelect != current.timeSelect,
                  builder: (context, state) {
                    final _value = growthType(
                      state.timeSelect,
                      state.fundDetailModel,
                    );
                    return _item(
                      title:
                          '${VPFundLocalize.current.growth} ${state.timeSelect.getTitle}',
                      customWidgetContent:
                          _value != null
                              ? FundGrowthWidget(growth: _value)
                              : null,
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  num? growthType(
    MonthForSortEnum timeSelect,
    FundDetailModel? fundDetailModel,
  ) {
    switch (timeSelect) {
      case MonthForSortEnum.threeMonths:
        return fundDetailModel?.growth3months;
      case MonthForSortEnum.sixMonths:
        return fundDetailModel?.growth6months;
      case MonthForSortEnum.nineMonths:
        return fundDetailModel?.growth9months;
      case MonthForSortEnum.oneYear:
        return fundDetailModel?.growth12months;
      default:
        return fundDetailModel?.growthYTD;
    }
  }

  Widget _item({
    required String title,
    String value = '',
    Widget? customWidgetContent,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundAccentGreen,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const SizedBox(height: 8),
          Text(
            title,
            style: vpTextStyle.caption2Medium?.copyWith(
              color: vpColor.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          customWidgetContent ??
              Text(
                value,
                style: vpTextStyle.subtitle14?.copyWith(
                  color: vpColor.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
