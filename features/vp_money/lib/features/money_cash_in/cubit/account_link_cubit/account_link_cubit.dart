import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/features/money_cash_in/webview/status_error.dart';
import 'package:vp_money/features/money_cash_in/webview_page_v2.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/request/money_cash_in/link_account_dialog_param.dart';
import 'package:vp_money/model/response/money_cash_in/money_bank.dart';

part 'account_link_cubit.freezed.dart';
part 'account_link_state.dart';

class AccountLinkCubit extends Cubit<AccountLinkState> {
  AccountLinkCubit() : super(const AccountLinkState());
  final MoneyRepository _repository = GetIt.instance<MoneyRepository>();

  Future<void> handleConfirmLinkRelation() async {
    try {
      emit(state.copyWith(isLoadingOverlay: true));
      var res = await _repository.postConfirmLinkRelationWithVPBank();
      if (!res.isSuccess &&
          !StatusErrorCashIn().isCheckStatus(res.code) &&
          StatusErrorCashIn().isCheckCABERR001405(res.code)) {
        emit(
          state.copyWith(
            isLoadingOverlay: false,
            errorMessageDialog: res.message.toString(),
            titleErrorMessageDialog: S.current.money_title_error_bank,
          ),
        );
        return;
      }
      if (!res.isSuccess &&
          !StatusErrorCashIn().isCheckStatus(res.code) &&
          !StatusErrorCashIn().isCheckCABERR001302(res.code)) {
        emit(
          state.copyWith(
            isLoadingOverlay: false,
            errorMessageDialog: res.message.toString(),
            titleErrorMessageDialog: S.current.money_an_error_occurred,
          ),
        );
        return;
      }
      if (res.data != null) {
        emit(
          state.copyWith(
            isLoadingOverlay: false,
            linkAccountDialog: LinkAccountDialodParam(
              responseCode: res.code,
              webViewPageV2Arg: WebViewPageV2Arg(location: res.data?.location),
            ),
          ),
        );
      } else {
        emit(
          state.copyWith(
            isLoadingOverlay: false,
            errorMessage: await getErrorMessage(res.message.toString()),
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoadingOverlay: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  void unLinkedAccount(MoneyBank bank) async {
    try {
      emit(state.copyWith(isLoadingOverlay: true));

      if (bank.getPartnerCodeStatus != PartnerCodeStatus.VPB) {
        // response = await repository.putDeregisterMBBank(
        //   idBank: bank.id.toString(),
        // );
      } else {
        var response = await _repository.putDeregister(
          idBank: bank.id.toString(),
        );

        emit(state.copyWith(isLoadingOverlay: false));
        if (response?.isSuccess ?? false) {
          emit(state.copyWith(unLinkedAccountSucess: true));
        } else {
          emit(state.copyWith(unLinkedAccountFail: true));
        }
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoadingOverlay: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  Future<void> shareQrcode(ScreenshotController screenshotController) async {
    final nameFile = DateTime.now().millisecondsSinceEpoch;
    try {
      final capturedImage = await screenshotController.capture(
        delay: const Duration(milliseconds: 10),
      );
      if (capturedImage != null) {
        var params = ShareParams(
          files: [
            XFile.fromData(
              capturedImage,
              name: 'qrcode_vpbanks_$nameFile.png',
              mimeType: 'image/png',
            ),
          ],
        );
        await SharePlus.instance.share(params);
      }
    } catch (e) {
      // Handle error if needed
    }
  }

  Future<void> saveImageQr(
    ScreenshotController screenshotController, {
    String? accountNumber,
  }) async {
    try {
      final capturedImage = await screenshotController.capture(
        delay: const Duration(milliseconds: 10),
      );
      if (capturedImage != null) {
        final fileName =
            accountNumber != null
                ? 'VPB-$accountNumber'
                : 'qrcode_vpbanks_${DateTime.now().millisecondsSinceEpoch}';

        await ImageGallerySaverPlus.saveImage(
          capturedImage.buffer.asUint8List(),
          name: "$fileName.jpeg",
        );
        emit(state.copyWith(successMessage: S.current.money_download_success));
      }
    } catch (e) {
      emit(state.copyWith(errorMessage: "Tải xuống thất bại"));
    }
  }

  void openWebview({required WebViewPageV2Arg webViewPageV2Arg}) {
    emit(state.copyWith(webViewPageV2Arg: webViewPageV2Arg));
  }

  void resetErrorMessage() {
    emit(AccountLinkState());
  }

  void resetSuccessMessage() {
    emit(state.copyWith(successMessage: null));
  }
}
