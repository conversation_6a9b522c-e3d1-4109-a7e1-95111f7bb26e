import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/core/common/widget/date_time_holder_view.dart';
import 'package:vp_money/core/common/widget/money_button_default.dart';
import 'package:vp_money/features/money_history/enum/money_account_type_enum.dart';
import 'package:vp_money/features/money_history/enum/money_time_filter_enum.dart';
import 'package:vp_money/features/money_history/widget/money_one_select_item.dart';
import 'package:vp_money/generated/assets.gen.dart';
import 'package:vp_money/generated/l10n.dart';

class MoneyHistoryModalFilter extends StatefulWidget {
  const MoneyHistoryModalFilter({
    super.key,
    required this.moneyTimeFilterInit,
    required this.accountTypeInit,
    this.dateTimeRangeCustom,
  });
  final MoneyTimeFilterEnum moneyTimeFilterInit;
  final AccountTypeEnum accountTypeInit;
  final DateTimeRange? dateTimeRangeCustom;

  @override
  State<MoneyHistoryModalFilter> createState() =>
      _MoneyHistoryModalFilterState();
}

class _MoneyHistoryModalFilterState extends State<MoneyHistoryModalFilter> {
  late AccountTypeEnum _accountTypeInit;
  late MoneyTimeFilterEnum _moneyTimeFilterInit;
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now();
  @override
  void initState() {
    _accountTypeInit = widget.accountTypeInit;
    _moneyTimeFilterInit = widget.moneyTimeFilterInit;
    if (widget.dateTimeRangeCustom != null) {
      _startDate = widget.dateTimeRangeCustom?.start ?? DateTime.now();
      _endDate = widget.dateTimeRangeCustom?.end ?? DateTime.now();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24),
      decoration: BoxDecoration(
        color: colorUtils.bgPopup,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: IntrinsicHeight(
        child: Column(
          children: [
            Center(
              child: Assets.icons.handle.svg(
                color: colorUtils.buttonTopBottomSheet,
              ),
            ),
            Stack(
              children: [
                buildListFilter(),
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /*----- Build tim kiem theo tai khoan va theo thoi gian 1 thang -> 6 thang ------*/
  Padding buildListFilter() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          buildTitle(S.current.money_filter_with_sub_account),
          const SizedBox(height: 16),
          buildMutilListSelectSubAccount(),
          const SizedBox(height: 24),
          buildTitle(S.current.money_filter_with_time),
          const SizedBox(height: 16),
          buildListSelectTime(),
          const SizedBox(height: 16),
          if (_moneyTimeFilterInit == MoneyTimeFilterEnum.custom)
            buildFileterDateTime(),

          const SizedBox(height: 40),
          buildBottomAction(),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  VPDateTimeHolderView buildFileterDateTime() {
    return VPDateTimeHolderView(
      startDate: _startDate,
      endDate: _endDate,
      minDate: DateTime(1970),
      onDateTimeChanged: (data) {
        if (data.endDate.difference(data.startDate).inDays.abs() > (6 * 30)) {
          VPPopup.oneButton(
            title: S.current.money_transfer_attention,
            content: "Vui lòng chọn khoảng thời gian nhỏ hơn 6 tháng",
          ).icFail.btnClose.showDialog(context);
        }
        setState(() {
          _startDate = data.startDate;
          _endDate = data.endDate;
        });
      },
    );
  }

  /*---------- Build Title ----------*/
  Text buildTitle(String keyText) {
    return Text(
      keyText,
      style: context.textStyle.subtitle14?.copyWith(
        color: Theme.of(context).black,
      ),
    );
  }

  /*---------- Chon tai khoan thuong, ky quy ----------*/
  Widget buildMutilListSelectSubAccount() {
    var derivativeAccount =
        GetIt.instance<SubAccountCubit>().derivativeActiveAccount;
    return Row(
      spacing: 8.0,
      children:
          AccountTypeEnum.values
              .where(
                (type) =>
                    !(type == AccountTypeEnum.derivative &&
                        derivativeAccount == null),
              )
              .map((type) {
                return MoneyOneSelectItem<AccountTypeEnum>(
                  value: type,
                  selected: _accountTypeInit == type,
                  onSelected: (e) {
                    setState(() {
                      _accountTypeInit = e;
                    });
                  },
                  displayText: type.displayName,
                );
              })
              .toList(),
    );
  }

  /*---------- Bottom action ----------*/
  Widget buildBottomAction() {
    final colorUtils = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: MoneyButtonDefault(
            haveBorder: true,
            color: colorUtils.bgPopup,
            colorTitle: colorUtils.gray700,
            press: () {
              setState(() {
                _accountTypeInit = AccountTypeEnum.normal;
                _moneyTimeFilterInit = MoneyTimeFilterEnum.oneMonth;
              });
            },
            text: S.current.money_make_new,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: MoneyButtonDefault(
            color: colorUtils.primary,
            press: () {
              if (_moneyTimeFilterInit == MoneyTimeFilterEnum.custom) {
                if (_endDate.difference(_startDate).inDays.abs() > (6 * 30)) {
                  VPPopup.oneButton(
                    title: S.current.money_transfer_attention,
                    content: "Vui lòng chọn khoảng thời gian nhỏ hơn 6 tháng",
                  ).icFail.btnClose.showDialog(context);
                  return;
                }
                context.pop(
                  Tuple3(
                    _accountTypeInit,
                    _moneyTimeFilterInit,
                    DateTimeRange(start: _startDate, end: _endDate),
                  ),
                );
              } else {
                context.pop(
                  Tuple3(_accountTypeInit, _moneyTimeFilterInit, null),
                );
              }
            },
            text: S.current.money_apply,
          ),
        ),
      ],
    );
  }

  buildListSelectTime() {
    return Wrap(
      spacing: 8.0,
      children:
          MoneyTimeFilterEnum.values.map((type) {
            return MoneyOneSelectItem<MoneyTimeFilterEnum>(
              value: type,
              selected: _moneyTimeFilterInit == type,
              onSelected: (e) {
                setState(() {
                  _moneyTimeFilterInit = e;
                });
              },
              displayText: type.displayName,
            );
          }).toList(),
    );
  }
}
