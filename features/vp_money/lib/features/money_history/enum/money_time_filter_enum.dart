import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_money/generated/l10n.dart';

enum MoneyTimeFilterEnum { oneMonth, twoMonths, threeMonths, sixMonths, custom }

extension MoneyTimeFilterEnumExt on MoneyTimeFilterEnum {
  String get displayName {
    switch (this) {
      case MoneyTimeFilterEnum.oneMonth:
        return '1 ${S.current.money_month}';
      case MoneyTimeFilterEnum.twoMonths:
        return '2 ${S.current.money_month}';
      case MoneyTimeFilterEnum.threeMonths:
        return '3 ${S.current.money_month}';
      case MoneyTimeFilterEnum.sixMonths:
        return '6 ${S.current.money_month}';
      case MoneyTimeFilterEnum.custom:
        return S.current.money_custom;
    }
  }

  /// Helper method to format timezone offset from DateTime
  static String _formatTimezoneOffset(DateTime dateTime) {
    final timeZoneOffset = dateTime.timeZoneOffset;
    final offsetHours = timeZoneOffset.inHours;
    final offsetMinutes = timeZoneOffset.inMinutes.remainder(60);
    final offsetSign = timeZoneOffset.isNegative ? '-' : '+';
    return '$offsetSign${offsetHours.abs().toString().padLeft(2, '0')}:${offsetMinutes.abs().toString().padLeft(2, '0')}';
  }

  String get fromDate {
    // Get current local time (with current timezone)
    final now = DateTime.now();

    // Calculate from date based on filter type, preserving current time
    final DateTime fromDateTime;
    switch (this) {
      case MoneyTimeFilterEnum.oneMonth:
        fromDateTime = DateTime(
          now.year,
          now.month - 1,
          now.day,
          now.hour,
          now.minute,
          now.second,
        );
        break;
      case MoneyTimeFilterEnum.twoMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 2,
          now.day,
          now.hour,
          now.minute,
          now.second,
        );
        break;
      case MoneyTimeFilterEnum.threeMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 3,
          now.day,
          now.hour,
          now.minute,
          now.second,
        );
        break;
      case MoneyTimeFilterEnum.sixMonths:
        fromDateTime = DateTime(
          now.year,
          now.month - 6,
          now.day,
          now.hour,
          now.minute,
          now.second,
        );
        break;
      case MoneyTimeFilterEnum.custom:
        fromDateTime = DateTime(
          now.year,
          now.month - 1,
          now.day,
          now.hour,
          now.minute,
          now.second,
        );
        break;
    }

    // Format with current timezone offset
    final offsetString = _formatTimezoneOffset(fromDateTime);
    return '${AppTimeUtils.formatTime(fromDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$offsetString';
  }

  String get toDate {
    // Get current local time (with current timezone)
    final now = DateTime.now();

    // Format with current timezone offset
    final offsetString = _formatTimezoneOffset(now);
    return '${AppTimeUtils.formatTime(now.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$offsetString';
  }

 
  static Map<String, String> getCustomDateRange(DateTimeRange dateRange) {
    // Get current time to use for both start and end dates
    final now = DateTime.now();

    // Start date: use current time instead of hardcoded 00:00:00
    final startDateTime = DateTime(
      dateRange.start.year,
      dateRange.start.month,
      dateRange.start.day,
      now.hour,
      now.minute,
      now.second,
    );

    // End date: use current time instead of hardcoded 23:59:59
    final endDateTime = DateTime(
      dateRange.end.year,
      dateRange.end.month,
      dateRange.end.day,
      now.hour,
      now.minute,
      now.second,
    );

    // Format with current timezone offsets
    final startOffsetString = _formatTimezoneOffset(startDateTime);
    final endOffsetString = _formatTimezoneOffset(endDateTime);

    final fromDate =
        '${AppTimeUtils.formatTime(startDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$startOffsetString';
    final toDate =
        '${AppTimeUtils.formatTime(endDateTime.toString(), format: AppTimeUtilsFormat.dateTimeStandard)}$endOffsetString';

    return {'fromDate': fromDate, 'toDate': toDate};
  }
}
