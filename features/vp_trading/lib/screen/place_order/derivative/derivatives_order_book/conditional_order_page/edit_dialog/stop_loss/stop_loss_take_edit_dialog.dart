import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_trading/core/constant/time_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class StopLossTakeEditDialog extends StatefulWidget {
  const StopLossTakeEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<StopLossTakeEditDialog> createState() => _StopLossTakeEditDialogState();
}

class _StopLossTakeEditDialogState extends State<StopLossTakeEditDialog> {
  final _volumeFocusNode = FocusNode();
  final _orderPriceFocusNode = FocusNode();
  final _takeProfitPriceFocusNode = FocusNode();
  final _stopLossPriceFocusNode = FocusNode();
  final _activationStopLossPriceFocusNode = FocusNode();

  late final TextEditingController _volumeController;
  late final TextEditingController _orderPriceController;
  late final TextEditingController _takeProfitPriceController;
  late final TextEditingController _stopLossPriceController;
  late final TextEditingController _activationStopLossPriceController;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing data
    _volumeController = TextEditingController(
      text: widget.model?.qty?.toString() ?? '',
    );
    _orderPriceController = TextEditingController(
      text: widget.model?.price ?? '',
    );
    _takeProfitPriceController = TextEditingController(
      text: widget.model?.activepriceTP?.toString() ?? '',
    );
    _stopLossPriceController = TextEditingController(
      text: widget.model?.stopLosses2?.toString() ?? '',
    );
    _activationStopLossPriceController = TextEditingController(
      text: widget.model?.stopLosses?.toString() ?? '',
    );

    // Initialize validation cubit with existing data if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<DerivativeValidateConditionOrderCubit>();

      // Initialize take profit price
      if (widget.model?.activepriceTP != null) {
        cubit.onChangePrice(widget.model!.activepriceTP!.toString());
      }

      // Initialize stop loss price
      if (widget.model?.stopLosses2 != null) {
        cubit.onChangePrice(
          widget.model!.stopLosses2!.toString(),
          stopLoss: true,
        );
      }

      // Initialize activation stop loss price
      if (widget.model?.stopLosses != null) {
        cubit.onChangePrice(
          widget.model!.stopLosses!.toString(),
          conditionStopLoss: true,
        );
      }
    });
  }

  @override
  void dispose() {
    _volumeFocusNode.dispose();
    _orderPriceFocusNode.dispose();
    _takeProfitPriceFocusNode.dispose();
    _stopLossPriceFocusNode.dispose();
    _activationStopLossPriceFocusNode.dispose();
    _volumeController.dispose();
    _orderPriceController.dispose();
    _takeProfitPriceController.dispose();
    _stopLossPriceController.dispose();
    _activationStopLossPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDialogTitle(),
        const SizedBox(height: 8),
        _buildInformationSection(),
        const SizedBox(height: 8),

        // Input Fields Section
        _buildInputFieldsWithListeners(),
        const SizedBox(height: 16),

        // Action Buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildDialogTitle() {
    return Center(
      child: Text(
        'Sửa lệnh',
        style: context.textStyle.subtitle16?.copyWith(
          color: vpColor.textPrimary,
          fontWeight: FontWeight.w700,
          fontSize: 20,
        ),
      ),
    );
  }

  Widget _buildInformationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Order Type Information
        _buildInformationRow(
          label: 'Loại lệnh',
          value: widget.model?.conditionOrderTypeFuEnum.title ?? '',
        ),
        const SizedBox(height: 8),

        // Contract Code Information
        _buildInformationRow(
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '',
        ),
        const SizedBox(height: 8),

        // Volume Information
        _buildInformationRow(
          label: 'Khối lượng đặt lệnh',
          value: widget.model?.qty?.toString() ?? '',
        ),
        const SizedBox(height: 8),

        // Order Price Information
        _buildInformationRow(
          label: 'Giá đặt',
          value: widget.model?.price ?? '',
        ),
        const SizedBox(height: 8),

        // Take Profit Price Information
        _buildInformationRow(
          label: 'Giá đặt chốt lời',
          value: widget.model?.stopPrice?.toString() ?? '',
        ),
      ],
    );
  }

  Widget _buildInformationRow({required String label, required String value}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsWithListeners() {
    return MultiBlocListener(
      listeners: _buildBlocListeners(),
      child: Column(children: [_buildInputFieldsSection()]),
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      _buildTakeProfitPriceListener(),
      _buildStopLossPriceListener(),
      _buildActivationStopLossPriceListener(),
    ];
  }

  BlocListener _buildTakeProfitPriceListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentTakeProfit != current.currentTakeProfit,
      listener: (context, state) {
        if (state.currentTakeProfit != null &&
            state.currentTakeProfit?.isNotEmpty == true) {
          _takeProfitPriceController.text = state.currentTakeProfit!;
          _takeProfitPriceController.selection = TextSelection.fromPosition(
            TextPosition(offset: _takeProfitPriceController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildStopLossPriceListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentStopLoss != current.currentStopLoss,
      listener: (context, state) {
        if (state.currentStopLoss != null &&
            state.currentStopLoss?.isNotEmpty == true) {
          _stopLossPriceController.text = state.currentStopLoss!;
          _stopLossPriceController.selection = TextSelection.fromPosition(
            TextPosition(offset: _stopLossPriceController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildActivationStopLossPriceListener() {
    return BlocListener<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentConditionStopLoss !=
              current.currentConditionStopLoss,
      listener: (context, state) {
        if (state.currentConditionStopLoss != null &&
            state.currentConditionStopLoss?.isNotEmpty == true) {
          _activationStopLossPriceController.text =
              state.currentConditionStopLoss!;
          _activationStopLossPriceController
              .selection = TextSelection.fromPosition(
            TextPosition(
              offset: _activationStopLossPriceController.text.length,
            ),
          );
        }
      },
    );
  }

  Widget _buildInputFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStopLossPriceInput(),
        const SizedBox(height: 8),
        _buildActivationStopLossPriceInput(),
      ],
    );
  }

  Widget _buildInputLabel(String text) {
    return Expanded(
      flex: 1,
      child: Text(
        text,
        style: context.textStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
    );
  }

  Widget _buildStopLossPriceInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildInputLabel('Giá cắt lỗ'),
            const SizedBox(width: 16),
            SizedBox(
              width: 160,
              child: InputFieldBox(
                controller: _stopLossPriceController,
                hintText: '',
                onChange: (value) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .onChangePrice(value, stopLoss: true);
                },
                focusNode: _stopLossPriceFocusNode,
                onTap: (increase) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .priceTap(
                        text: _stopLossPriceController.text,
                        increase: increase,
                        stopLoss: true,
                      );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  LengthLimitingTextInputFormatter(8),
                  ...priceInputFormatter,
                ],
              ),
            ),
          ],
        ),
        _buildStopLossPriceErrorDisplay(),
      ],
    );
  }

  Widget _buildActivationStopLossPriceInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildInputLabel('Giá kích hoạt cắt lỗ'),
            const SizedBox(width: 16),
            SizedBox(
              width: 160,
              child: InputFieldBox(
                controller: _activationStopLossPriceController,
                hintText: '',
                onChange: (value) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .onChangePrice(value, conditionStopLoss: true);
                },
                focusNode: _activationStopLossPriceFocusNode,
                onTap: (increase) {
                  context
                      .read<DerivativeValidateConditionOrderCubit>()
                      .priceTap(
                        text: _activationStopLossPriceController.text,
                        increase: increase,
                        conditionStopLoss: true,
                      );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  LengthLimitingTextInputFormatter(8),
                  ...priceInputFormatter,
                ],
              ),
            ),
          ],
        ),
        _buildActivationStopLossPriceErrorDisplay(),
      ],
    );
  }

  Widget _buildStopLossPriceErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorStopLoss != current.errorStopLoss ||
              previous.currentStopLoss != current.currentStopLoss,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorStopLoss.message,
          text: _stopLossPriceController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildActivationStopLossPriceErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorConditionStopLoss !=
                  current.errorConditionStopLoss ||
              previous.currentConditionStopLoss !=
                  current.currentConditionStopLoss,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorConditionStopLoss.message,
          text: _activationStopLossPriceController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return BlocBuilder<
      DerivativeValidateConditionOrderCubit,
      DerivativeValidateConditionOrderState
    >(
      builder: (context, validateState) {
        return BlocBuilder<
          DerivativeConditionOrderEditCubit,
          DerivativeConditionOrderEditState
        >(
          builder: (context, editState) {
            final isValid = _isFormValid(validateState);

            return _buildButtonRow(isValid, editState, validateState);
          },
        );
      },
    );
  }

  bool _isFormValid(DerivativeValidateConditionOrderState validateState) {
    return !validateState.errorStopLoss.isError &&
        !validateState.errorConditionStopLoss.isError &&
        _stopLossPriceController.text.isNotEmpty &&
        _activationStopLossPriceController.text.isNotEmpty &&
        _hasFieldsChanged();
  }

  bool _hasFieldsChanged() {
    final originalStopLossPrice = widget.model?.stopLosses2?.toString() ?? '';
    final originalActivationStopLossPrice =
        widget.model?.stopLosses?.toString() ?? '';

    final currentStopLossPrice =
        _stopLossPriceController.text.priceDerivative?.toString() ?? '';
    final currentActivationStopLossPrice =
        _activationStopLossPriceController.text.priceDerivative?.toString() ??
        '';

    return originalStopLossPrice != currentStopLossPrice ||
        originalActivationStopLossPrice != currentActivationStopLossPrice;
  }

  Widget _buildButtonRow(
    bool isValid,
    DerivativeConditionOrderEditState editState,
    DerivativeValidateConditionOrderState validateState,
  ) {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed:
                editState.canPerformAction
                    ? () {
                      context.pop();
                    }
                    : null,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            disabled: !isValid || !editState.canPerformAction,
            onPressed:
                isValid && editState.canPerformAction
                    ? () {
                      _handleConfirm(context, validateState);
                    }
                    : null,
          ),
        ),
      ],
    );
  }

  void _handleConfirm(
    BuildContext context,
    DerivativeValidateConditionOrderState validateState,
  ) {
    if (widget.model == null) return;

    final request = ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderId: widget.model!.orderId ?? '',
      accountId: GetIt.instance<SubAccountCubit>().derivativeAccount?.id ?? "",
      orderType: widget.model!.orderType ?? '',
      conditionInfo: ConditionInfo(
        symbol: widget.model!.symbol ?? '',
        qty: widget.model!.qty ?? 0,
        side: widget.model!.orderTypeFUEnum.codeRequestCondition.toLowerCase(),
        type: "limit",
        price: widget.model!.price ?? '',
        fromDate: widget.model!.fromDate ?? '',
        toDate: widget.model!.toDate ?? '',
        priceTP: widget.model!.stopPrice?.priceDerivative.toString(),
        priceSL: _stopLossPriceController.text.priceDerivative,
        activepriceSL: _activationStopLossPriceController.text.priceDerivative,
        timetype: TimeType.T.toServer,
        split: widget.model!.split,
      ),
    );

    context
        .read<DerivativeConditionOrderEditCubit>()
        .editDerivativeConditionOrder(request: request);
  }
}
