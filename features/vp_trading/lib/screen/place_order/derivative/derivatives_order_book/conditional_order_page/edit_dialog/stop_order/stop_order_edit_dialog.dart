import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/time_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/awaiting/widget/choice_equal_buy_button.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class StopOrderEditDialog extends StatefulWidget {
  const StopOrderEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<StopOrderEditDialog> createState() => _StopOrderEditDialogState();
}

class _StopOrderEditDialogState extends State<StopOrderEditDialog> {
  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final _volumeFocusNode = FocusNode();
  final _orderPriceFocusNode = FocusNode();
  final _activationPriceFocusNode = FocusNode();
  late final TextEditingController _volumeController;
  late final TextEditingController _orderPriceController;
  late final TextEditingController _activationPriceController;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing data
    _volumeController = TextEditingController(
      text: widget.model?.qty?.toString() ?? '',
    );
    _orderPriceController = TextEditingController(
      text: widget.model?.price ?? '',
    );
    _activationPriceController = TextEditingController(
      text: widget.model?.stopPrice?.toString() ?? '',
    );

    _activationPriceFocusNode.addListener(_focusListener);

    // Initialize validation cubit with existing data if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<DerivativeValidateOrderCubit>();

      // Initialize order price
      if (widget.model?.price != null) {
        cubit.onChangePrice(widget.model!.price!);
      }

      // Initialize activation price - use stopPrice for Stop Order
      if (widget.model?.stopPrice != null) {
        cubit.onChangeActivePrice(widget.model!.stopPrice!);
      }

      // Set activation type from model
      if (widget.model?.activeType != null) {
        final activationType =
            widget.model!.activeType == 'UP'
                ? ActivationConditionsType.greaterThan
                : ActivationConditionsType.lessThan;
        cubit.setActivationConditions(activationType);
      }
    });
  }

  void _focusListener() {
    if (_activationPriceFocusNode.hasFocus) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );
    } else {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void dispose() {
    _volumeFocusNode.dispose();
    _orderPriceFocusNode.dispose();
    _activationPriceFocusNode.dispose();
    _priceBlink.dispose();
    _volumeController.dispose();
    _orderPriceController.dispose();
    _activationPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDialogTitle(),
        const SizedBox(height: 8),
        _buildInformationSection(),
        const SizedBox(height: 8),

        // Input Fields Section
        _buildInputFieldsWithListeners(),
        const SizedBox(height: 16),

        // Action Buttons
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildDialogTitle() {
    return Center(
      child: Text(
        'Sửa lệnh',
        style: context.textStyle.subtitle16?.copyWith(
          color: vpColor.textPrimary,
          fontWeight: FontWeight.w700,
          fontSize: 20,
        ),
      ),
    );
  }

  Widget _buildInputFieldsWithListeners() {
    return MultiBlocListener(
      listeners: _buildBlocListeners(),
      child: Column(children: [_buildInputFieldsSection()]),
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      _buildActivationPriceListener(),
      _buildOrderPriceListener(),
      _buildVolumeListener(),
      _buildFocusListener(),
    ];
  }

  BlocListener _buildActivationPriceListener() {
    return BlocListener<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentActivePrice != current.currentActivePrice,
      listener: (context, state) {
        if (state.currentActivePrice != null &&
            state.currentActivePrice?.isNotEmpty == true) {
          _activationPriceController.text = state.currentActivePrice!;
          _activationPriceController.selection = TextSelection.fromPosition(
            TextPosition(offset: _activationPriceController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildOrderPriceListener() {
    return BlocListener<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      listenWhen:
          (previous, current) => previous.currentPrice != current.currentPrice,
      listener: (context, state) {
        if (state.currentPrice != null) {
          _orderPriceController.text = state.currentPrice!;
          _orderPriceController.selection = TextSelection.fromPosition(
            TextPosition(offset: _orderPriceController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildVolumeListener() {
    return BlocListener<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.currentVolume != current.currentVolume,
      listener: (context, state) {
        if (state.currentVolume != null) {
          _volumeController.text = state.currentVolume!;
          _volumeController.selection = TextSelection.fromPosition(
            TextPosition(offset: _volumeController.text.length),
          );
        }
      },
    );
  }

  BlocListener _buildFocusListener() {
    return BlocListener<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      listenWhen:
          (previous, current) =>
              previous.focusKeyboard != current.focusKeyboard,
      listener: (context, state) {
        if (state.focusKeyboard == FocusKeyboard.priceActive) {
          _activationPriceFocusNode.requestFocus();
        }
      },
    );
  }

  Widget _buildInformationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Order Type Information
        _buildInformationRow(
          label: 'Loại lệnh',
          value: widget.model?.conditionOrderTypeFuEnum.title ?? '',
        ),
        const SizedBox(height: 8),

        // Contract Code Information
        _buildInformationRow(
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '',
        ),
      ],
    );
  }

  Widget _buildInformationRow({required String label, required String value}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: vpColor.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildVolumeInput(),
        const SizedBox(height: 8),
        _buildOrderPriceInput(),
        const SizedBox(height: 8),
        _buildActivationPriceInput(),
      ],
    );
  }

  Widget _buildInputLabel(String text) {
    return Expanded(
      flex: 1,
      child: Text(
        text,
        style: context.textStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
    );
  }

  Widget _buildVolumeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [_buildVolumeInputRow(), _buildVolumeErrorDisplay()],
    );
  }

  Widget _buildVolumeInputRow() {
    return Row(
      children: [
        _buildInputLabel('Khối lượng đặt'),
        const SizedBox(width: 16),
        _buildVolumeInputField(),
      ],
    );
  }

  Widget _buildVolumeInputField() {
    return SizedBox(
      width: 160,
      child: InputFieldBox(
        controller: _volumeController,
        hintText: '',
        onChange: (value) {
          context.read<DerivativeValidateOrderCubit>().onChangeVolume(value);
        },
        focusNode: _volumeFocusNode,
        onTap: (increase) {
          context.read<DerivativeValidateOrderCubit>().volumeTap(
            text: _volumeController.text,
            increase: increase,
          );
        },
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(8),
        ],
      ),
    );
  }

  Widget _buildOrderPriceInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                'Giá đặt',
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textSecondary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 160,
              child: InputFieldBox(
                controller: _orderPriceController,
                hintText: '',
                onChange: (value) {
                  context.read<DerivativeValidateOrderCubit>().onChangePrice(
                    value,
                  );
                },
                focusNode: _orderPriceFocusNode,
                onTap: (increase) {
                  context.read<DerivativeValidateOrderCubit>().priceTap(
                    text: _orderPriceController.text,
                    increase: increase,
                    activation: false,
                  );
                },
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  LengthLimitingTextInputFormatter(8),
                  ...priceInputFormatter,
                ],
              ),
            ),
          ],
        ),
        _buildOrderPriceErrorDisplay(),
      ],
    );
  }

  Widget _buildActivationPriceInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,

      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                'Giá kích hoạt',
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textSecondary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Row(
              children: [
                // Greater Than Equal Button
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: vpColor.backgroundElevation1,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const ButtonGreaterThan(),
                ),
                const SizedBox(width: 4),
                SizedBox(
                  width: 160,
                  child: InputFieldBox(
                    controller: _activationPriceController,
                    hintText: '',
                    onChange: (value) {
                      context
                          .read<DerivativeValidateOrderCubit>()
                          .onChangeActivePrice(value);
                    },
                    focusNode: _activationPriceFocusNode,
                    onTap: (increase) {
                      context.read<DerivativeValidateOrderCubit>().priceTap(
                        text: _activationPriceController.text,
                        increase: increase,
                        activation: true,
                      );
                    },
                    inputFormatters: [
                      removeZeroStartInputFormatter,
                      LengthLimitingTextInputFormatter(8),
                      ...priceInputFormatter,
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        _buildActivationPriceErrorDisplay(),
      ],
    );
  }

  Widget _buildVolumeErrorDisplay() {
    return Align(
      alignment: Alignment.centerRight,
      child: BlocBuilder<
        DerivativeValidateOrderCubit,
        DerivativeValidateOrderState
      >(
        buildWhen:
            (previous, current) =>
                previous.errorVolume != current.errorVolume ||
                previous.currentVolume != current.currentVolume,
        builder: (context, state) {
          final cubit = context.read<DerivativeValidateOrderCubit>();
          final maxVolumeValue = cubit.maxVolume().toString();

          return InputFieldError(
            errorMessage: state.errorVolume.message(maxVolumeValue),
            text: _volumeController.text,
            isShake: true,
            textAlign: TextAlign.end,
          );
        },
      ),
    );
  }

  Widget _buildOrderPriceErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorPrice != current.errorPrice ||
              previous.currentPrice != current.currentPrice,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorPrice.message,
          text: _orderPriceController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildActivationPriceErrorDisplay() {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      buildWhen:
          (previous, current) =>
              previous.errorActivePrice != current.errorActivePrice ||
              previous.currentActivePrice != current.currentActivePrice,
      builder: (context, state) {
        return InputFieldError(
          errorMessage: state.errorActivePrice.message,
          text: _activationPriceController.text,
          isShake: true,
          textAlign: TextAlign.end,
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      builder: (context, validateState) {
        return BlocBuilder<
          DerivativeConditionOrderEditCubit,
          DerivativeConditionOrderEditState
        >(
          builder: (context, editState) {
            final isValid = _isFormValid(validateState);

            return _buildButtonRow(isValid, editState, validateState);
          },
        );
      },
    );
  }

  bool _isFormValid(DerivativeValidateOrderState validateState) {
    return !validateState.errorActivePrice.isError &&
        !validateState.errorPrice.isError &&
        !validateState.errorVolume.isError &&
        _activationPriceController.text.isNotEmpty &&
        _orderPriceController.text.isNotEmpty &&
        _volumeController.text.isNotEmpty &&
        _hasFieldsChanged();
  }

  bool _hasFieldsChanged() {
    final originalVolume = widget.model?.qty?.toString() ?? '';
    final originalPrice = widget.model?.price?.priceDerivative;
    final originalActivationPrice = widget.model?.stopPrice?.priceDerivative;

    final currentVolume = _volumeController.text;
    final currentPrice = _orderPriceController.text.priceDerivative;
    final currentActivationPrice =
        _activationPriceController.text.priceDerivative;

    return originalVolume != currentVolume ||
        originalPrice != currentPrice ||
        originalActivationPrice != currentActivationPrice;
  }

  Widget _buildButtonRow(
    bool isValid,
    DerivativeConditionOrderEditState editState,
    DerivativeValidateOrderState validateState,
  ) {
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed:
                editState.canPerformAction
                    ? () {
                      context.pop();
                    }
                    : null,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            disabled: !isValid || !editState.canPerformAction,
            onPressed:
                isValid && editState.canPerformAction
                    ? () {
                      _handleConfirm(context, validateState);
                    }
                    : null,
          ),
        ),
      ],
    );
  }

  void _handleConfirm(
    BuildContext context,
    DerivativeValidateOrderState validateState,
  ) {
    if (widget.model == null) return;

    final request = ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderId: widget.model!.orderId ?? '',
      accountId: GetIt.instance<SubAccountCubit>().derivativeAccount?.id ?? "",
      orderType: widget.model!.orderType ?? '',
      conditionInfo: ConditionInfo(
        symbol: widget.model!.symbol ?? '',
        qty: int.tryParse(_volumeController.text) ?? widget.model!.qty ?? 0,
        side: widget.model!.orderTypeFUEnum.codeRequestCondition.toLowerCase(),
        type: "limit",
        price:
            (_orderPriceController.text.isNotEmpty
                    ? _orderPriceController.text.priceDerivative
                    : widget.model!.price?.priceDerivative)
                .toString(),
        fromDate: widget.model!.fromDate ?? '',
        toDate: widget.model!.toDate ?? '',
        activePrice: _activationPriceController.text.priceDerivative,
        activeType: validateState.activationType.toParamRequest(),
        timetype: TimeType.T.toServer,
      ),
    );

    context
        .read<DerivativeConditionOrderEditCubit>()
        .editDerivativeConditionOrder(request: request);
  }
}

class ButtonGreaterThan extends StatelessWidget {
  const ButtonGreaterThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icGreaterThanEqual.path,
          isFocus: activationType == ActivationConditionsType.greaterThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(
                    ActivationConditionsType.greaterThan,
                  ),
        );
      },
    );
  }
}

class ButtonLessThan extends StatelessWidget {
  const ButtonLessThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icLessThanEqual.path,
          isFocus: activationType == ActivationConditionsType.lessThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(ActivationConditionsType.lessThan),
        );
      },
    );
  }
}
