import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/generated/l10n.dart';

class ConditionOrderTitle extends StatelessWidget {
  const ConditionOrderTitle({
    super.key,
    required this.expandTitleWidget,
    this.onDeleteAll,
    this.showTitleDeleteAll,
    this.isShowEditCancel = true,
  });
  final List<int> expandTitleWidget;
  final VoidCallback? onDeleteAll;
  final bool? showTitleDeleteAll;
  final bool? isShowEditCancel;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Row(
        children: [
          Expanded(
            flex: expandTitleWidget[0],
            child: _Text(
              title: VPTradingLocalize.current.trading_code_title,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: expandTitleWidget[1],
            child: _Text(
              title: VPTradingLocalize.current.trading_bid_price_title,
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            flex: expandTitleWidget[2],
            child: _Text(
              title: VPTradingLocalize.current.trading_vol_title,
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            flex: expandTitleWidget[3],
            child: const _Text(
              title: "Ngày hết hiệu lực",
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}

class _Text extends StatelessWidget {
  final String title;
  final TextAlign? textAlign;

  const _Text({super.key, required this.title, this.textAlign});

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      textAlign: textAlign,
      style: vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
    );
  }
}
