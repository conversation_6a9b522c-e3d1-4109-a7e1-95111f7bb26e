// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'condition_order_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConditionOrderRequestModel _$ConditionOrderRequestModelFromJson(
  Map<String, dynamic> json,
) => ConditionOrderRequestModel(
  requestId: json['requestId'] as String?,
  market: json['market'] as String?,
  via: json['via'] as String? ?? 'V',
  orderType: json['orderType'] as String,
  accountId: json['accountId'] as String,
  conditionInfo: ConditionInfo.fromJson(
    json['conditionInfo'] as Map<String, dynamic>,
  ),
  orderId: json['orderId'] as String?,
);

Map<String, dynamic> _$ConditionOrderRequestModelToJson(
  ConditionOrderRequestModel instance,
) => <String, dynamic>{
  if (instance.requestId case final value?) 'requestId': value,
  'accountId': instance.accountId,
  if (instance.market case final value?) 'market': value,
  'via': instance.via,
  'orderType': instance.orderType,
  'conditionInfo': instance.conditionInfo,
  if (instance.orderId case final value?) 'orderId': value,
};

ConditionInfo _$ConditionInfoFromJson(Map<String, dynamic> json) =>
    ConditionInfo(
      symbol: json['symbol'] as String,
      qty: json['qty'] as num,
      side: json['side'] as String,
      type: json['type'] as String,
      price: json['price'],
      fromDate: json['fromDate'] as String,
      toDate: json['toDate'] as String,
      activePrice: json['activePrice'] as num?,
      activeType: json['activeType'] as String?,
      slipPagePrice: json['slipPagePrice'] as num?,
      stopLossRate: json['stopLossRate'] as num?,
      stopLossPriceAmp: json['stopLossPriceAmp'] as num?,
      costPrice: json['costPrice'] as num?,
      priceTypeTP: json['priceTypeTP'] as String?,
      priceTypeSL: json['priceTypeSL'] as String?,
      priceTP: json['priceTP'],
      priceSL: json['priceSL'] as num?,
      activepriceTP: json['activepriceTP'] as num?,
      activepriceSL: json['activepriceSL'] as num?,
      timetype: json['timetype'] as String?,
      split: json['split'] as String?,
      priceStep: json['priceStep'] as num?,
      deltaValue: json['deltaValue'] as num?,
      deltaType: json['deltaType'] as String?,
    );

Map<String, dynamic> _$ConditionInfoToJson(
  ConditionInfo instance,
) => <String, dynamic>{
  'symbol': instance.symbol,
  'qty': instance.qty,
  'side': instance.side,
  'type': instance.type,
  if (instance.price case final value?) 'price': value,
  if (instance.activePrice case final value?) 'activePrice': value,
  if (instance.activeType case final value?) 'activeType': value,
  'fromDate': instance.fromDate,
  'toDate': instance.toDate,
  if (instance.slipPagePrice case final value?) 'slipPagePrice': value,
  if (instance.stopLossRate case final value?) 'stopLossRate': value,
  if (instance.stopLossPriceAmp case final value?) 'stopLossPriceAmp': value,
  if (instance.costPrice case final value?) 'costPrice': value,
  if (instance.priceTypeTP case final value?) 'priceTypeTP': value,
  if (instance.priceTypeSL case final value?) 'priceTypeSL': value,
  if (instance.priceTP case final value?) 'priceTP': value,
  if (instance.priceSL case final value?) 'priceSL': value,
  if (instance.activepriceTP case final value?) 'activepriceTP': value,
  if (instance.activepriceSL case final value?) 'activepriceSL': value,
  if (instance.timetype case final value?) 'timetype': value,
  if (instance.split case final value?) 'split': value,
  if (instance.priceStep case final value?) 'priceStep': value,
  if (instance.deltaValue case final value?) 'deltaValue': value,
  if (instance.deltaType case final value?) 'deltaType': value,
};
