// ignore_for_file: noop_primitive_operations

import 'package:intl/intl.dart';

class AppTimeUtils {
  static String getDateTimeStringFull({DateTime? dateTime}) {
    dateTime ??= DateTime.now();
    return AppTimeUtils.formatTime(dateTime.toString(),
        format: AppTimeUtilsFormat.dateWithFullTime);
  }

  static String formatTime(
    String timeStamp, {
    String format = AppTimeUtilsFormat.dateWithTime,
  }) {
    try {
      return DateFormat(format).format(DateTime.parse(timeStamp));
    } catch (e) {
      return '';
    }
  }

  static String differentMonth(DateTime? from, DateTime? to) {
    try {
      from = DateTime(from!.year, from.month, from.day);
      to = DateTime(to!.year, to.month, to.day);
      final toLastDay = DateTime(to.year, to.month + 1, 0).day;
      final fromLastDay = DateTime(from.year, from.month + 1, 0).day;
      return ((to.year - from.year) * 12 +
              (to.month - from.month) +
              (to.day / toLastDay - from.day / fromLastDay))
          .toStringAsFixed(1)
          .replaceAll(RegExp(r'.0$'), '');
      // double diff = ((to.difference(from).inHours / 24).round() / 30).abs();
      // return diff.toStringAsFixed(1).replaceAll(RegExp(r'.0$'), '');
    } catch (e) {
      return '';
    }
  }

  static DateTime? parseDateTimeString(
    String? source, {
    String format = AppTimeUtilsFormat.dateNormal,
  }) {
    try {
      return DateFormat(format).parse(source!);
    } catch (e) {
      return null;
    }
  }

  static String getDateTimeString({DateTime? dateTime}) {
    dateTime ??= DateTime.now();
    return AppTimeUtils.formatTime(
      dateTime.toString(),
      format: AppTimeUtilsFormat.dateNormal,
    );
  }

  static String parseStringToString(String? source, String from, String to) {
    try {
      final date = DateFormat(from).parse(source!);
      return DateFormat(to).format(date);
    } catch (e) {
      return '';
    }
  }

  //Trả về ngày hôm sau
  static String parseStringTakeTheNextDay(String date) {
    try {
      final tempDate = DateFormat(AppTimeUtilsFormat.dateNormal).parse(date);
      return getDateTimeString(
        dateTime: DateTime(tempDate.year, tempDate.month, tempDate.day + 1),
      );
    } catch (e) {
      return '';
    }
  }

  static String format(DateTime? dateTime, String to) {
    if (dateTime != null) {
      try {
        return DateFormat(to).format(dateTime);
      } catch (e) {
        return '';
      }
    }
    return '';
  }

  static int sortDateDesc(String date1, String date2) {
    return parseDateTimeString(date1)!.isAfter(parseDateTimeString(date2)!)
        ? 1
        : 0;
  }

  static int sortDateAsc(String date1, String date2) {
    return parseDateTimeString(date1)!.isBefore(parseDateTimeString(date2)!)
        ? 1
        : 0;
  }

  //Lay moc thoi gian theo yeu cau theo thang
  static String prevMonth(int prevNum, DateTime currentDateTime) {
    final prevDateTime = DateTime(
      currentDateTime.year,
      currentDateTime.month - prevNum,
      currentDateTime.day,
    );
    return AppTimeUtils.formatTime(
      prevDateTime.toString(),
      format: AppTimeUtilsFormat.dateNormal,
    );
  }

  //Lay moc thoi gian tuong lai
  static String nextYear(int nextYear, DateTime currentDateTime) {
    final prevDateTime = DateTime(
      currentDateTime.year + nextYear,
      currentDateTime.month,
      currentDateTime.day,
    );
    return AppTimeUtils.formatTime(
      prevDateTime.toString(),
      format: AppTimeUtilsFormat.dateNormal,
    );
  }

  /// Format from TimeStamp
  static String timeFromTimeStamp(
    int timeStamp, {
    String format = AppTimeUtilsFormat.dateNormal,
  }) {
    try {
      final date =
          DateTime.fromMillisecondsSinceEpoch(timeStamp.toInt() * 1000);
      return DateFormat(format).format(date);
    } catch (e) {
      return '';
    }
  }

  /// Convert hour
  static String formatHour(String value) {
    var result = value;
    if (value.contains(':')) {
      result = value.replaceAll(':', 'h');
    }
    return result;
  }

  static bool isToday(DateTime? date) {
    if (date != null) {
      final now = DateTime.now();
      return date.day == now.day &&
          date.month == now.month &&
          date.year == now.year;
    } else {
      return false;
    }
  }

  static String prevMonthIso8601String(int prevNum, DateTime currentDateTime) {
    final prevDateTime = DateTime(
      currentDateTime.year,
      currentDateTime.month - prevNum,
      currentDateTime.day,
    );

    
    final timeZoneOffset = prevDateTime.timeZoneOffset;
    final offsetHours = timeZoneOffset.inHours;
    final offsetMinutes = timeZoneOffset.inMinutes.remainder(60);

    // Format timezone offset (e.g., +07:00, -05:30)
    final offsetSign = timeZoneOffset.isNegative ? '-' : '+';
    final offsetString =
        '$offsetSign${offsetHours.abs().toString().padLeft(2, '0')}:${offsetMinutes.abs().toString().padLeft(2, '0')}';

    return '${AppTimeUtils.formatTime(
      prevDateTime.toString(),
      format: AppTimeUtilsFormat.dateTimeStandard,
    )}$offsetString';
  }
}

class AppTimeUtilsFormat {
  static const dateNormal = 'dd/MM/yyyy';
  static const dateWithTime = 'dd/MM/yyyy HH:mm';
  static const dateWithFullTime = 'dd/MM/yyyy HH:mm:ss';
  static const dateWithSimpleYear = 'dd/MM/yy';
  static const dateWithUtc = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
  static const dateRC = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'";
  static const dateWithStt = "yyyy-MM-dd'T'HH:mm'Z'";
  static const dateWithEvent = "yyyy-MM-dd'T'HH:mm:ss'Z'";
  static const dateConditionOrder = 'dd-MM-yyyy';
  static const time = 'HH:mm';
  static const dateTimeCustomerCare = 'dd-MM-yyyy HH:mm:ss';
  static const dateTimeBondPortfolio = 'yyyy-MM-dd';
  static const dateUtc = 'yyyy-MM-dd HH:mm:ss.SSS';
  static const timeHHmmss = 'HH:mm:ss';
  static const dateYMD = 'yyyy-MM-dd';
  static const dateTimeStandard = "yyyy-MM-dd'T'HH:mm:ss";
  static const dateTimeHHmmddMMyyyy = 'hh:mm dd/mm/yyyy';
}
