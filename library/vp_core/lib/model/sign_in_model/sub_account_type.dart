import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_core/vp_core.dart';

part 'sub_account_type.g.dart';

@HiveType(typeId: HiveTypeKey.subAccountType)
enum SubAccountType {
  @HiveField(0)
  @JsonValue('.1')
  normal,
  @HiveField(1)
  @JsonValue('.6')
  margin,
  @HiveField(2)
  @JsonValue('.3')
  bond,
  @HiveField(3)
  @JsonValue('.8')
  derivative,
  @HiveField(4)
  @JsonValue('-1')
  all,
  @HiveField(5)
  @JsonValue('..')
  commission,
  @HiveField(6)
  @JsonValue('.9')
  wealth;

  static SubAccountType? fromCodeToEnum(String code) {
    switch (code) {
      case 'NN': // Standard/Normal account
        return SubAccountType.normal;
      case 'NM': // Margin account
        return SubAccountType.margin;
      case 'FB': // Bond account
        return SubAccountType.bond;
      case 'PS': // Derivative account
        return SubAccountType.derivative;
      case 'CS': // Entrusted/Commission account
        return SubAccountType.commission;
      case 'WE': // Wealth account
        return SubAccountType.wealth;
      default:
        return null;
    }
  }

  String get shortName {
    return switch (this) {
      normal => 'TK thường',
      margin => 'TK ký quỹ',
      bond => 'TK trái phiếu',
      derivative => 'TK phái sinh',
      all => 'Tất cả tiểu khoản',
      commission => 'Tiểu khoản thường',
      wealth => 'Tiểu khoản tích sản',
    };
  }

  String get displayName {
    return switch (this) {
      normal => VPCommonLocalize.current.subAccount_standard,
      margin => VPCommonLocalize.current.subAccount_margin,
      bond => VPCommonLocalize.current.subAccount_bond,
      derivative => VPCommonLocalize.current.subAccount_derivative,
      all => 'Tất cả tiểu khoản',
      commission => 'Tiểu khoản thường',
      wealth => 'Tiểu khoản tích sản',
    };
  }

  String get code {
    return switch (this) {
      normal => 'NN',
      margin => 'NM',
      bond => 'FB',
      derivative => 'PS',
      commission => 'CS',
      all => '-1',
      wealth => 'WE',
    };
  }

  String get defaultSetting {
    return switch (this) { normal => '1', margin => '2', _ => '' };
  }
}

enum SubAccountProductTypeCdEnum {
  derivative, // PS
  standard, // NN
  margin, // NM
  bond, // FB
  entrusted, // CS
  wealth, // WE
}

extension SubAccountProductTypeCdEnumExtension on SubAccountProductTypeCdEnum {
  static SubAccountProductTypeCdEnum? fromCodeToEnum(String code) {
    switch (code) {
      case 'PS':
        return SubAccountProductTypeCdEnum.derivative;
      case 'NN':
        return SubAccountProductTypeCdEnum.standard;
      case 'NM':
        return SubAccountProductTypeCdEnum.margin;
      case 'FB':
        return SubAccountProductTypeCdEnum.bond;
      case 'CS':
        return SubAccountProductTypeCdEnum.entrusted;
      case 'WE':
        return SubAccountProductTypeCdEnum.wealth;
      default:
        return null;
    }
  }

  String get displayName {
    switch (this) {
      case SubAccountProductTypeCdEnum.derivative:
        return VPCommonLocalize.current.subAccount_derivative;
      case SubAccountProductTypeCdEnum.standard:
        return VPCommonLocalize.current.subAccount_standard;

      case SubAccountProductTypeCdEnum.margin:
        return VPCommonLocalize.current.subAccount_margin;

      case SubAccountProductTypeCdEnum.bond:
        return VPCommonLocalize.current.subAccount_bond;

      case SubAccountProductTypeCdEnum.entrusted:
        return VPCommonLocalize.current.subAccount_entrusted;

      case SubAccountProductTypeCdEnum.wealth:
        return 'Tiểu khoản tích sản';
    }
  }

  String get shortName {
    switch (this) {
      case SubAccountProductTypeCdEnum.derivative:
        return 'TKPS';
      case SubAccountProductTypeCdEnum.standard:
        return 'TKCS';
      case SubAccountProductTypeCdEnum.margin:
        return 'TKKQ';
      case SubAccountProductTypeCdEnum.bond:
        return 'TKTP';
      case SubAccountProductTypeCdEnum.entrusted:
        return 'TKUT';
      case SubAccountProductTypeCdEnum.wealth:
        return 'TKW';
    }
  }

  String get code {
    switch (this) {
      case SubAccountProductTypeCdEnum.derivative:
        return 'PS';
      case SubAccountProductTypeCdEnum.standard:
        return 'NN';
      case SubAccountProductTypeCdEnum.margin:
        return 'NM';
      case SubAccountProductTypeCdEnum.bond:
        return 'FB';
      case SubAccountProductTypeCdEnum.entrusted:
        return 'CS';
      case SubAccountProductTypeCdEnum.wealth:
        return 'WE';
    }
  }
}
